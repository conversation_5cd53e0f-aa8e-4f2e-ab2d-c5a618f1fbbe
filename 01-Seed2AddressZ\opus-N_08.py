#!/usr/bin/env python3
"""
Zanjani Project
Universal NON-BIP39 Cryptocurrency Wallet Recovery System v4.0
Enhanced version with complete BTC address types and TRC20 token support
Ensures all possible address formats are generated
"""

import os
import sys
import json
import time
import hashlib
import hmac
import struct
import csv
import base58
import binascii
from datetime import datetime
from typing import List, Dict, Tuple, Optional, Set, Any
from concurrent.futures import ThreadPoolExecutor, as_completed
from dataclasses import dataclass, field
from enum import Enum
import secrets

# Third-party imports
try:
    import requests
    from colorama import init, Fore, Style, Back
    from tqdm import tqdm
    from bip32utils import BIP32Key, BIP32_HARDEN
    from ecdsa import SigningKey, SECP256k1
    import bech32
    from Crypto.Hash import keccak
except ImportError as e:
    print(f"Missing required library: {e}")
    print("Install with: pip install requests colorama tqdm bip32utils ecdsa bech32 pycryptodome base58")
    sys.exit(1)

# Initialize colorama
init(autoreset=True)

# Constants
BITCOIN_MAINNET_PRIVATE = 0x0488ADE4
BITCOIN_MAINNET_PUBLIC = 0x0488B21E
HARDENED_OFFSET = 0x80000000

class Network(Enum):
    """Supported blockchain networks"""
    BITCOIN = "bitcoin"
    TRON = "tron"

class AddressType(Enum):
    """Bitcoin address types - comprehensive list"""
    P2PKH = "p2pkh"  # Legacy (1...)
    P2PKH_UNCOMPRESSED = "p2pkh_uncompressed"  # Legacy uncompressed
    P2SH = "p2sh"  # Script hash (3...)
    P2SH_P2WPKH = "p2sh-p2wpkh"  # Wrapped SegWit (3...)
    P2WPKH = "p2wpkh"  # Native SegWit (bc1q...)
    P2WSH = "p2wsh"  # Native SegWit Script (bc1q...)
    P2TR = "p2tr"  # Taproot (bc1p...)

class SeedDerivationMethod(Enum):
    """Seed derivation methods matching original code"""
    SHA512_32 = "sha512_32"  # First 32 bytes of SHA512
    SHA512_64 = "sha512_64"  # Full 64 bytes of SHA512
    SHA256 = "sha256"
    PBKDF2_LEGACY = "pbkdf2_legacy"
    UTF8_SHA512 = "utf8_sha512"
    HMAC_SHA512 = "hmac_sha512"
    KECCAK512 = "keccak512"
    XOR_HASH = "xor_hash"

@dataclass
class DerivationPath:
    """BIP32 derivation path"""
    purpose: int
    coin_type: int
    account: int
    change: int
    address_index: int
    name: str = ""

    def __str__(self):
        return f"m/{self.purpose}'/{self.coin_type}'/{self.account}'/{self.change}/{self.address_index}"

    def to_bip32_path(self):
        """Convert to bip32utils format"""
        return [
            self.purpose + BIP32_HARDEN,
            self.coin_type + BIP32_HARDEN,
            self.account + BIP32_HARDEN,
            self.change,
            self.address_index
        ]

@dataclass
class WalletInfo:
    """Complete wallet information"""
    network: Network
    address_type: Optional[AddressType]
    derivation_path: str
    private_key: str
    public_key: str
    address: str
    wif: str
    seed_phrase: str
    seed_method: str
    compressed: bool = True
    balance: Optional[float] = None
    token_balances: Dict[str, float] = field(default_factory=dict)
    last_checked: Optional[datetime] = None

# Common TRC20 tokens including Tether
TRC20_TOKENS = {
    'TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t': {
        'symbol': 'USDT',
        'name': 'Tether USD',
        'decimals': 6
    },
    'TEkxiTehnzSmSe2XqrBj4w32RUN966rdz8': {
        'symbol': 'USDC',
        'name': 'USD Coin',
        'decimals': 6
    },
    'TNUC9Qb1rRpS5CbWLmNMxXBjyFoydXjWFR': {
        'symbol': 'WTRX',
        'name': 'Wrapped TRX',
        'decimals': 6
    }
}

class BalanceChecker:
    """Enhanced balance checker with TRC20 token support"""
    
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        })
        self.rate_limit_delay = 0.5
        
    def check_btc_balance(self, address: str) -> Optional[float]:
        """Check Bitcoin balance using multiple API sources"""
        api_sources = [
            {
                'name': 'Blockchain.info',
                'url': f'https://blockchain.info/q/addressbalance/{address}',
                'parser': lambda r: float(r.text) / 1e8
            },
            {
                'name': 'Blockchair',
                'url': f'https://api.blockchair.com/bitcoin/dashboards/address/{address}',
                'parser': lambda r: r.json()['data'][address]['address']['balance'] / 1e8
            }
        ]
        
        for source in api_sources:
            try:
                time.sleep(self.rate_limit_delay)
                response = self.session.get(source['url'], timeout=10)
                if response.status_code == 200:
                    balance = source['parser'](response)
                    return balance
            except Exception:
                continue
                
        return None
    
    def check_tron_balance_comprehensive(self, address: str) -> Tuple[Optional[float], Dict[str, Any]]:
        """Check TRON balance including all TRC20 tokens with emphasis on Tether"""
        try:
            time.sleep(self.rate_limit_delay)
            
            # Use TronGrid API
            headers = {
                'TRON-PRO-API-KEY': 'your-api-key-here'  # Optional, works without key too
            }
            
            # Get account info
            url = f"https://api.trongrid.io/v1/accounts/{address}"
            response = self.session.get(url, headers=headers, timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                if 'data' in data and len(data['data']) > 0:
                    account_data = data['data'][0]
                    trx_balance = account_data.get('balance', 0) / 1e6
                else:
                    trx_balance = 0
                
                token_balances = {}
                
                # Get TRC20 balances
                trc20_url = f"https://api.trongrid.io/v1/accounts/{address}/tokens"
                trc20_response = self.session.get(trc20_url, headers=headers, timeout=10)
                
                if trc20_response.status_code == 200:
                    trc20_data = trc20_response.json()
                    if 'data' in trc20_data:
                        for token in trc20_data['data']:
                            if token.get('balance', '0') != '0':
                                symbol = token.get('symbol', 'Unknown')
                                decimals = int(token.get('decimals', 0))
                                balance = int(token.get('balance', 0)) / (10 ** decimals)
                                
                                if balance > 0:
                                    token_balances[symbol] = {
                                        'balance': balance,
                                        'decimals': decimals,
                                        'contract': token.get('address', ''),
                                        'name': token.get('name', symbol)
                                    }
                
                return trx_balance, token_balances
                
        except Exception as e:
            pass
            
        return None, {}

class EnhancedNonBIP39Recovery:
    """Enhanced NON-BIP39 wallet recovery with complete address generation"""
    
    # Comprehensive derivation paths
    DERIVATION_PATHS = [
        # Direct paths (for older wallets)
        {"name": "Direct", "path": [], "bip32_path": ""},
        
        # Standard BIP paths for Bitcoin
        DerivationPath(44, 0, 0, 0, 0, "BIP44_BITCOIN"),
        DerivationPath(44, 0, 0, 0, 1, "BIP44_BITCOIN_1"),
        DerivationPath(44, 0, 0, 1, 0, "BIP44_BITCOIN_CHANGE"),
        DerivationPath(44, 0, 1, 0, 0, "BIP44_BITCOIN_ACC1"),
        
        # SegWit paths
        DerivationPath(49, 0, 0, 0, 0, "BIP49_SEGWIT"),
        DerivationPath(49, 0, 0, 0, 1, "BIP49_SEGWIT_1"),
        DerivationPath(49, 0, 0, 1, 0, "BIP49_SEGWIT_CHANGE"),
        DerivationPath(84, 0, 0, 0, 0, "BIP84_NATIVE_SEGWIT"),
        DerivationPath(84, 0, 0, 0, 1, "BIP84_NATIVE_SEGWIT_1"),
        DerivationPath(84, 0, 0, 1, 0, "BIP84_NATIVE_SEGWIT_CHANGE"),
        
        # Taproot
        DerivationPath(86, 0, 0, 0, 0, "BIP86_TAPROOT"),
        DerivationPath(86, 0, 0, 0, 1, "BIP86_TAPROOT_1"),
        
        # Legacy paths
        {"name": "Zero", "path": [0, 0], "bip32_path": "0/0"},
        {"name": "Zero_Hard", "path": [0], "bip32_path": "0'", "hardened": True},
        {"name": "Hardened_44_0", "path": [44 + HARDENED_OFFSET, 0 + HARDENED_OFFSET], "bip32_path": "44'/0'"},
        {"name": "Hardened_44_0_0", "path": [44 + HARDENED_OFFSET, 0 + HARDENED_OFFSET, 0 + HARDENED_OFFSET], "bip32_path": "44'/0'/0'"},
        {"name": "BIP44_Standard", "path": [44 + HARDENED_OFFSET, 0 + HARDENED_OFFSET, 0 + HARDENED_OFFSET, 0], "bip32_path": "44'/0'/0'/0"},
        
        # TRON paths
        DerivationPath(44, 195, 0, 0, 0, "BIP44_TRON"),
        DerivationPath(44, 195, 0, 0, 1, "BIP44_TRON_1"),
        DerivationPath(44, 195, 1, 0, 0, "BIP44_TRON_ACC1"),
    ]
    
    def __init__(self):
        """Initialize recovery system"""
        self.timestamp = str(int(time.time()))
        self.all_addresses = set()
        self.bitcoin_addresses = {}
        self.tron_addresses = {}
        self.balance_checker = BalanceChecker()
        
    def create_output_directory(self) -> str:
        """Create timestamped output directory"""
        dir_name = f"recovery_output_{self.timestamp}"
        os.makedirs(dir_name, exist_ok=True)
        return dir_name
    
    def get_seed_methods(self) -> Dict[str, callable]:
        """Get all seed derivation methods"""
        return {
            SeedDerivationMethod.SHA512_32.value: lambda phrase: hashlib.sha512(phrase.encode('utf-8')).digest()[:32],
            SeedDerivationMethod.SHA512_64.value: lambda phrase: hashlib.sha512(phrase.encode('utf-8')).digest(),
            SeedDerivationMethod.SHA256.value: lambda phrase: hashlib.sha256(phrase.encode('utf-8')).digest(),
            SeedDerivationMethod.PBKDF2_LEGACY.value: lambda phrase: hashlib.pbkdf2_hmac('sha512', phrase.encode('utf-8'), b'', 2048)[:32],
            SeedDerivationMethod.UTF8_SHA512.value: lambda phrase: phrase.encode('utf-8').ljust(32, b'\x00')[:32],
            SeedDerivationMethod.HMAC_SHA512.value: lambda phrase: hmac.new(b'Bitcoin seed', phrase.encode('utf-8'), hashlib.sha512).digest()[:32],
            SeedDerivationMethod.KECCAK512.value: lambda phrase: self._keccak512_hash(phrase.encode('utf-8'))[:32],
            SeedDerivationMethod.XOR_HASH.value: lambda phrase: self._xor_based_seed(phrase)
        }
    
    def _keccak512_hash(self, data: bytes) -> bytes:
        """Keccak-512 hash"""
        k = keccak.new(digest_bits=512)
        k.update(data)
        return k.digest()
    
    def _xor_based_seed(self, phrase: str) -> bytes:
        """XOR-based seed generation"""
        words = phrase.split()
        if not words:
            return hashlib.sha256(phrase.encode('utf-8')).digest()
        
        result = hashlib.sha256(words[0].encode('utf-8')).digest()
        for word in words[1:]:
            word_hash = hashlib.sha256(word.encode('utf-8')).digest()
            result = bytes(a ^ b for a, b in zip(result, word_hash))
        return result
    
    def derive_with_bip32utils(self, seed: bytes, derivation_info) -> Optional[Dict]:
        """Derive addresses using bip32utils"""
        try:
            # Ensure seed is correct length
            if len(seed) > 32:
                seed = seed[:32]
            
            master = BIP32Key.fromEntropy(seed)
            
            # Handle different path formats
            if isinstance(derivation_info, dict):
                path_name = derivation_info.get('name', 'Unknown')
                path_str = derivation_info.get('bip32_path', '')
                
                if not derivation_info.get('path'):
                    # Direct derivation
                    derived = master
                else:
                    # Follow path
                    derived = master
                    for index in derivation_info['path']:
                        derived = derived.ChildKey(index)
            else:
                # DerivationPath object
                path_name = derivation_info.name or str(derivation_info)
                path_str = str(derivation_info)
                derived = master
                
                for index in derivation_info.to_bip32_path():
                    derived = derived.ChildKey(index)
            
            # Get keys
            private_key = derived.PrivateKey()
            public_key = derived.PublicKey()
            
            # Generate all address types
            addresses = self._generate_all_addresses(private_key, public_key, compressed=True)
            
            # Also generate uncompressed addresses
            uncompressed_addresses = self._generate_all_addresses(private_key, public_key, compressed=False)
            
            # Combine results
            result = {
                'method': 'bip32utils',
                'path_name': path_name,
                'path_str': str(path_str),  # Ensure it's a string
                'compressed': True,
                'private_key_hex': private_key.hex(),
                'wif': derived.WalletImportFormat(),
                'wif_uncompressed': self.private_key_to_wif(private_key, compressed=False),
                'public_key_hex': public_key.hex(),
                **addresses,
                'p2pkh_uncompressed': uncompressed_addresses.get('p2pkh_address', ''),
                'tron_address': uncompressed_addresses.get('tron_address', '')
            }
            
            # Track addresses
            self._track_addresses(result)
            
            return result
            
        except Exception as e:
            return None
    
    def derive_with_ecdsa(self, seed: bytes, derivation_info) -> List[Dict]:
        """Derive addresses using ECDSA"""
        results = []
        
        try:
            # Construct path-specific seed
            if isinstance(derivation_info, dict):
                path_name = derivation_info.get('name', 'Unknown')
                path_str = derivation_info.get('bip32_path', '')
                path_data = b''.join(index.to_bytes(4, 'big') for index in derivation_info.get('path', []))
            else:
                path_name = derivation_info.name or str(derivation_info)
                path_str = str(derivation_info)
                path_data = b''.join(index.to_bytes(4, 'big') for index in derivation_info.to_bip32_path())
            
            # Create path-specific seed
            path_seed = seed + path_data if path_data else seed
            
            # Derive key using HMAC-SHA512
            key = hmac.new(b'Bitcoin seed', path_seed, hashlib.sha512).digest()
            
            # Split into private key and chain code
            private_key = key[:32]
            
            # Ensure valid private key
            n = 0xFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFEBAAEDCE6AF48A03BBFD25E8CD0364141
            while int.from_bytes(private_key, 'big') == 0 or int.from_bytes(private_key, 'big') >= n:
                private_key = hashlib.sha256(private_key).digest()
            
            # Create signing key
            sk = SigningKey.from_string(private_key, curve=SECP256k1)
            vk = sk.get_verifying_key()
            
            # Generate compressed addresses
            compressed_pubkey = self._get_compressed_pubkey(vk)
            compressed_addresses = self._generate_all_addresses(private_key, compressed_pubkey, compressed=True)
            
            compressed_result = {
                'method': 'ecdsa',
                'path_name': path_name,
                'path_str': str(path_str),  # Ensure it's a string
                'compressed': True,
                'private_key_hex': private_key.hex(),
                'wif': self.private_key_to_wif(private_key, compressed=True),
                'public_key_hex': compressed_pubkey.hex(),
                **compressed_addresses
            }
            results.append(compressed_result)
            
            # Generate uncompressed addresses
            uncompressed_pubkey = b'\x04' + vk.to_string()
            uncompressed_addresses = self._generate_all_addresses(private_key, uncompressed_pubkey, compressed=False)
            
            uncompressed_result = {
                'method': 'ecdsa',
                'path_name': path_name,
                'path_str': str(path_str),  # Ensure it's a string
                'compressed': False,
                'private_key_hex': private_key.hex(),
                'wif': self.private_key_to_wif(private_key, compressed=False),
                'public_key_hex': uncompressed_pubkey.hex(),
                **uncompressed_addresses
            }
            results.append(uncompressed_result)
            
            # Track addresses
            for result in results:
                self._track_addresses(result)
            
        except Exception as e:
            print(f"Error in ECDSA derivation: {e}")
            
        return results
    
    def _get_compressed_pubkey(self, verifying_key) -> bytes:
        """Get compressed public key"""
        x = verifying_key.pubkey.point.x()
        y = verifying_key.pubkey.point.y()
        prefix = b'\x02' if y % 2 == 0 else b'\x03'
        return prefix + x.to_bytes(32, 'big')
    
    def _generate_all_addresses(self, private_key: bytes, public_key: bytes, compressed: bool) -> Dict[str, str]:
        """Generate all possible address types"""
        addresses = {}
        
        # P2PKH (Legacy)
        addresses['p2pkh_address'] = self.generate_p2pkh_address(public_key)
        
        # Only generate these for compressed keys
        if compressed and len(public_key) == 33:
            addresses['p2sh_address'] = self.generate_p2sh_address(public_key)
            addresses['p2wpkh_address'] = self.generate_p2wpkh_address(public_key)
            addresses['p2wsh_address'] = self.generate_p2wsh_address(public_key)
            addresses['p2tr_address'] = self.generate_p2tr_address(public_key)
        else:
            addresses['p2sh_address'] = ""
            addresses['p2wpkh_address'] = ""
            addresses['p2wsh_address'] = ""
            addresses['p2tr_address'] = ""
        
        # TRON (only for uncompressed)
        if not compressed and len(public_key) == 65:
            addresses['tron_address'] = self.generate_tron_address(public_key)
        else:
            addresses['tron_address'] = ""
        
        return addresses
    
    def _track_addresses(self, result: Dict):
        """Track generated addresses"""
        for key, value in result.items():
            if 'address' in key and value and not value.startswith('Error'):
                self.all_addresses.add(value)
                
                if value.startswith('T'):
                    self.tron_addresses[value] = result
                else:
                    self.bitcoin_addresses[value] = result
    
    def private_key_to_wif(self, private_key: bytes, compressed: bool = True) -> str:
        """Convert private key to WIF"""
        prefix = b'\x80'
        
        if compressed:
            extended = prefix + private_key + b'\x01'
        else:
            extended = prefix + private_key
        
        checksum = hashlib.sha256(hashlib.sha256(extended).digest()).digest()[:4]
        return base58.b58encode(extended + checksum).decode('ascii')
    
    def generate_p2pkh_address(self, public_key: bytes) -> str:
        """Generate P2PKH address"""
        try:
            pubkey_hash = hashlib.sha256(public_key).digest()
            h = hashlib.new('ripemd160')
            h.update(pubkey_hash)
            pubkey_hash = h.digest()
            
            versioned = b'\x00' + pubkey_hash
            checksum = hashlib.sha256(hashlib.sha256(versioned).digest()).digest()[:4]
            
            return base58.b58encode(versioned + checksum).decode('ascii')
        except:
            return ""
    
    def generate_p2sh_address(self, public_key: bytes) -> str:
        """Generate P2SH-P2WPKH address"""
        try:
            # Create P2WPKH script
            pubkey_hash = hashlib.sha256(public_key).digest()
            h = hashlib.new('ripemd160')
            h.update(pubkey_hash)
            pubkey_hash = h.digest()
            
            # P2WPKH script: OP_0 <20-byte-key-hash>
            script = b'\x00\x14' + pubkey_hash
            
            # Hash the script
            script_hash = hashlib.sha256(script).digest()
            h = hashlib.new('ripemd160')
            h.update(script_hash)
            script_hash = h.digest()
            
            # Create address
            versioned = b'\x05' + script_hash
            checksum = hashlib.sha256(hashlib.sha256(versioned).digest()).digest()[:4]
            
            return base58.b58encode(versioned + checksum).decode('ascii')
        except:
            return ""
    
    def generate_p2wpkh_address(self, public_key: bytes) -> str:
        """Generate P2WPKH address"""
        try:
            pubkey_hash = hashlib.sha256(public_key).digest()
            h = hashlib.new('ripemd160')
            h.update(pubkey_hash)
            pubkey_hash = h.digest()
            
            return bech32.encode('bc', 0, pubkey_hash)
        except:
            return ""
    
    def generate_p2wsh_address(self, public_key: bytes) -> str:
        """Generate P2WSH address"""
        try:
            # Create script
            script = b'\x21' + public_key + b'\xac'
            
            # SHA256 hash
            script_hash = hashlib.sha256(script).digest()
            
            return bech32.encode('bc', 0, script_hash)
        except:
            return ""
    
    def generate_p2tr_address(self, public_key: bytes) -> str:
        """Generate P2TR address"""
        try:
            # Extract x-coordinate
            if len(public_key) == 33 and public_key[0] in [0x02, 0x03]:
                x_coord = public_key[1:33]
            elif len(public_key) == 65 and public_key[0] == 0x04:
                x_coord = public_key[1:33]
            else:
                return ""
            
            return bech32.encode('bc', 1, x_coord)
        except:
            return ""
    
    def generate_tron_address(self, public_key: bytes) -> str:
        """Generate TRON address"""
        try:
            # Must be uncompressed
            if len(public_key) != 65 or public_key[0] != 0x04:
                return ""
            
            # Remove prefix
            pub_key = public_key[1:]
            
            # Keccak256 hash
            k = keccak.new(digest_bits=256)
            k.update(pub_key)
            hash_result = k.digest()
            
            # Take last 20 bytes and add TRON prefix
            address_bytes = b'\x41' + hash_result[-20:]
            
            # Double SHA256 for checksum
            checksum = hashlib.sha256(hashlib.sha256(address_bytes).digest()).digest()[:4]
            
            return base58.b58encode(address_bytes + checksum).decode('ascii')
        except:
            return ""
    
    def process_mnemonic_with_all_methods(self, mnemonic: str) -> List[Dict]:
        """Process a mnemonic with all seed methods and derivation paths"""
        print(f"\n{Fore.CYAN}Processing: {mnemonic[:50]}...{Style.RESET_ALL}")
        
        all_results = []
        seed_methods = self.get_seed_methods()
        
        # Progress bar
        total_combinations = len(seed_methods) * len(self.DERIVATION_PATHS)
        with tqdm(total=total_combinations, desc="Testing combinations", unit="combo") as pbar:
            
            for seed_method_name, seed_func in seed_methods.items():
                try:
                    # Generate seed
                    seed = seed_func(mnemonic.strip().lower())
                    
                    # Try all derivation paths
                    for derivation_info in self.DERIVATION_PATHS:
                        
                        # Method 1: bip32utils
                        bip32_result = self.derive_with_bip32utils(seed, derivation_info)
                        if bip32_result:
                            bip32_result['mnemonic'] = mnemonic
                            bip32_result['seed_method'] = seed_method_name
                            all_results.append(bip32_result)
                        
                        # Method 2: ECDSA
                        ecdsa_results = self.derive_with_ecdsa(seed, derivation_info)
                        for result in ecdsa_results:
                            result['mnemonic'] = mnemonic
                            result['seed_method'] = seed_method_name
                            all_results.append(result)
                        
                        pbar.update(1)
                        
                except Exception as e:
                    pbar.update(1)
                    continue
        
        print(f"{Fore.GREEN}Generated {len(all_results)} combinations{Style.RESET_ALL}")
        return all_results
    
    def check_all_balances(self, results: List[Dict], output_base: str):
        """Check balances for all addresses with focus on USDT"""
        print(f"\n{Fore.YELLOW}🔍 Checking balances...{Style.RESET_ALL}")
        
        # Collect unique addresses
        bitcoin_addresses = set()
        tron_addresses = set()
        
        for result in results:
            for addr_key in ['p2pkh_address', 'p2sh_address', 'p2wpkh_address', 'p2wsh_address', 'p2tr_address', 'p2pkh_uncompressed']:
                addr = result.get(addr_key, '')
                if addr and not addr.startswith('T'):
                    bitcoin_addresses.add(addr)
            
            tron_addr = result.get('tron_address', '')
            if tron_addr:
                tron_addresses.add(tron_addr)
        
        balance_results = []
        
        # Check Bitcoin addresses
        if bitcoin_addresses:
            print(f"Checking {len(bitcoin_addresses)} Bitcoin addresses...")
            with tqdm(total=len(bitcoin_addresses), desc="Bitcoin", unit="addr") as pbar:
                for addr in bitcoin_addresses:
                    balance = self.balance_checker.check_btc_balance(addr)
                    balance_results.append({
                        'address': addr,
                        'network': 'Bitcoin',
                        'balance': balance if balance is not None else 0,
                        'unit': 'BTC',
                        'tokens': {}
                    })
                    pbar.update(1)
        
        # Check TRON addresses with comprehensive token support
        if tron_addresses:
            print(f"Checking {len(tron_addresses)} TRON addresses (including USDT)...")
            with tqdm(total=len(tron_addresses), desc="TRON", unit="addr") as pbar:
                for addr in tron_addresses:
                    trx_balance, token_balances = self.balance_checker.check_tron_balance_comprehensive(addr)
                    balance_results.append({
                        'address': addr,
                        'network': 'TRON',
                        'balance': trx_balance if trx_balance is not None else 0,
                        'unit': 'TRX',
                        'tokens': token_balances
                    })
                    pbar.update(1)
        
        # Export balance results
        self._export_balance_results(balance_results, output_base)
        
        return balance_results
    
    def _export_balance_results(self, balance_results: List[Dict], output_base: str):
        """Export balance results with special attention to USDT"""
        # All balances
        balance_file = f"{output_base}_balances.csv"
        with open(balance_file, 'w', newline='', encoding='utf-8') as f:
            writer = csv.writer(f)
            writer.writerow(['address', 'network', 'balance', 'unit', 'tokens'])
            
            for result in balance_results:
                tokens_str = json.dumps(result['tokens']) if result['tokens'] else ''
                writer.writerow([
                    result['address'],
                    result['network'],
                    result['balance'],
                    result['unit'],
                    tokens_str
                ])
        
        # Addresses with balance (including tokens)
        funded_addresses = []
        usdt_addresses = []
        
        for result in balance_results:
            has_balance = False
            
            # Check main balance
            if result['balance'] > 0:
                has_balance = True
                funded_addresses.append({
                    'address': result['address'],
                    'network': result['network'],
                    'balance': result['balance'],
                    'unit': result['unit'],
                    'type': 'native'
                })
            
            # Check token balances
            for token_symbol, token_info in result['tokens'].items():
                if isinstance(token_info, dict):
                    balance = token_info.get('balance', 0)
                else:
                    balance = token_info
                
                if balance > 0:
                    has_balance = True
                    funded_addresses.append({
                        'address': result['address'],
                        'network': result['network'],
                        'balance': balance,
                        'unit': token_symbol,
                        'type': 'token'
                    })
                    
                    # Special tracking for USDT
                    if token_symbol == 'USDT':
                        usdt_addresses.append({
                            'address': result['address'],
                            'balance': balance,
                            'contract': token_info.get('contract', TRC20_TOKENS['TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t']['symbol'])
                        })
        
        # Export funded addresses
        if funded_addresses:
            funded_file = f"{output_base}_funded_addresses.csv"
            with open(funded_file, 'w', newline='', encoding='utf-8') as f:
                writer = csv.DictWriter(f, fieldnames=['address', 'network', 'balance', 'unit', 'type'])
                writer.writeheader()
                writer.writerows(funded_addresses)
            
            print(f"{Fore.GREEN}✓ Found {len(funded_addresses)} addresses with balance!{Style.RESET_ALL}")
        
        # Export USDT addresses specifically
        if usdt_addresses:
            usdt_file = f"{output_base}_usdt_addresses.csv"
            with open(usdt_file, 'w', newline='', encoding='utf-8') as f:
                writer = csv.DictWriter(f, fieldnames=['address', 'balance', 'contract'])
                writer.writeheader()
                writer.writerows(usdt_addresses)
            
            print(f"{Fore.GREEN}✓ Found {len(usdt_addresses)} addresses with USDT!{Style.RESET_ALL}")
            
            # Print USDT details
            total_usdt = sum(addr['balance'] for addr in usdt_addresses)
            print(f"{Fore.YELLOW}Total USDT found: {total_usdt:,.2f} USDT{Style.RESET_ALL}")
    
    def export_addresses_only(self, output_base: str):
        """Export address-only files"""
        unique_addresses = sorted(list(self.all_addresses))
        valid_addresses = [addr for addr in unique_addresses if addr and len(addr) > 25]
        
        if not valid_addresses:
            print(f"{Fore.YELLOW}No valid addresses to export{Style.RESET_ALL}")
            return
        
        # Separate by type
        bitcoin_addresses = [addr for addr in valid_addresses if not addr.startswith('T')]
        tron_addresses = [addr for addr in valid_addresses if addr.startswith('T')]
        
        # Export all addresses
        self._export_address_list(valid_addresses, f"{output_base}_all_addresses", "All")
        
        # Export Bitcoin addresses
        if bitcoin_addresses:
            self._export_address_list(bitcoin_addresses, f"{output_base}_bitcoin_addresses", "Bitcoin")
        
        # Export TRON addresses
        if tron_addresses:
            self._export_address_list(tron_addresses, f"{output_base}_tron_addresses", "TRON")
        
        # Export classified addresses
        self._export_classified_addresses(valid_addresses, output_base)
        
        # Print statistics
        self._print_address_statistics(valid_addresses, bitcoin_addresses, tron_addresses)
    
    def _export_address_list(self, addresses: List[str], base_name: str, network: str):
        """Export address list in multiple formats"""
        # CSV
        csv_file = f"{base_name}.csv"
        with open(csv_file, 'w', newline='', encoding='utf-8') as f:
            writer = csv.writer(f)
            writer.writerow(['address'])
            for addr in addresses:
                writer.writerow([addr])
        
        # TXT
        txt_file = f"{base_name}.txt"
        with open(txt_file, 'w', encoding='utf-8') as f:
            for addr in addresses:
                f.write(f"{addr}\n")
        
        # Batch (comma-separated)
        batch_file = f"{base_name}_batch.txt"
        with open(batch_file, 'w', encoding='utf-8') as f:
            f.write(','.join(addresses))
        
        print(f"{Fore.BLUE}Exported {len(addresses)} {network} addresses{Style.RESET_ALL}")
    
    def _export_classified_addresses(self, addresses: List[str], output_base: str):
        """Export addresses with type classification"""
        classified_file = f"{output_base}_addresses_classified.csv"
        with open(classified_file, 'w', newline='', encoding='utf-8') as f:
            writer = csv.writer(f)
            writer.writerow(['address', 'type', 'network', 'description'])
            
            for addr in addresses:
                if addr.startswith('1'):
                    writer.writerow([addr, 'P2PKH', 'Bitcoin', 'Legacy'])
                elif addr.startswith('3'):
                    writer.writerow([addr, 'P2SH', 'Bitcoin', 'Script Hash (possibly SegWit)'])
                elif addr.startswith('bc1q') and len(addr) == 42:
                    writer.writerow([addr, 'P2WPKH', 'Bitcoin', 'Native SegWit'])
                elif addr.startswith('bc1q') and len(addr) == 62:
                    writer.writerow([addr, 'P2WSH', 'Bitcoin', 'Native SegWit Script'])
                elif addr.startswith('bc1p'):
                    writer.writerow([addr, 'P2TR', 'Bitcoin', 'Taproot'])
                elif addr.startswith('T'):
                    writer.writerow([addr, 'TRON', 'TRON', 'TRON Network (TRX/TRC20)'])
                else:
                    writer.writerow([addr, 'Unknown', 'Unknown', 'Unknown format'])
    
    def _print_address_statistics(self, all_addresses: List[str], bitcoin_addresses: List[str], tron_addresses: List[str]):
        """Print detailed address statistics"""
        p2pkh_count = len([a for a in bitcoin_addresses if a.startswith('1')])
        p2sh_count = len([a for a in bitcoin_addresses if a.startswith('3')])
        p2wpkh_count = len([a for a in bitcoin_addresses if a.startswith('bc1q') and len(a) == 42])
        p2wsh_count = len([a for a in bitcoin_addresses if a.startswith('bc1q') and len(a) == 62])
        p2tr_count = len([a for a in bitcoin_addresses if a.startswith('bc1p')])
        
        print(f"\n{Fore.CYAN}📊 Address Statistics:{Style.RESET_ALL}")
        print(f"  Total unique addresses: {len(all_addresses)}")
        print(f"  Bitcoin addresses: {len(bitcoin_addresses)}")
        print(f"    - P2PKH (Legacy, 1...): {p2pkh_count}")
        print(f"    - P2SH (Script, 3...): {p2sh_count}")
        print(f"    - P2WPKH (SegWit, bc1q...): {p2wpkh_count}")
        print(f"    - P2WSH (SegWit Script, bc1q...): {p2wsh_count}")
        print(f"    - P2TR (Taproot, bc1p...): {p2tr_count}")
        print(f"  TRON addresses (T...): {len(tron_addresses)}")
    
    def generate_summary(self, results: List[Dict], output_base: str):
        """Generate comprehensive summary"""
        # Convert all path_str values to strings
        path_strings = []
        for r in results:
            path_str = r.get('path_str', '')
            if isinstance(path_str, list):
                path_strings.append(str(path_str))
            else:
                path_strings.append(str(path_str))
        
        summary = {
            'timestamp': datetime.now().isoformat(),
            'recovery_stats': {
                'total_mnemonics': len(set(r['mnemonic'] for r in results)),
                'total_combinations': len(results),
                'seed_methods_used': sorted(list(set(r['seed_method'] for r in results))),
                'derivation_paths_used': sorted(list(set(path_strings))),
            },
            'address_stats': {
                'total_unique': len(self.all_addresses),
                'bitcoin': {
                    'total': len(self.bitcoin_addresses),
                    'p2pkh': len([a for a in self.bitcoin_addresses if a.startswith('1')]),
                    'p2sh': len([a for a in self.bitcoin_addresses if a.startswith('3')]),
                    'p2wpkh': len([a for a in self.bitcoin_addresses if a.startswith('bc1q') and len(a) == 42]),
                    'p2wsh': len([a for a in self.bitcoin_addresses if a.startswith('bc1q') and len(a) == 62]),
                    'p2tr': len([a for a in self.bitcoin_addresses if a.startswith('bc1p')])
                },
                'tron': {
                    'total': len(self.tron_addresses)
                }
            },
            'files_generated': []
        }
        
        # Save summary
        summary_file = f"{output_base}_summary.json"
        with open(summary_file, 'w', encoding='utf-8') as f:
            json.dump(summary, f, indent=2)
        
        print(f"\n{Fore.GREEN}✓ Summary saved to {summary_file}{Style.RESET_ALL}")
        
        # Print summary
        print(f"\n{Fore.CYAN}{'='*60}")
        print(f"Recovery Summary")
        print(f"{'='*60}{Style.RESET_ALL}")
        print(f"Total mnemonics processed: {summary['recovery_stats']['total_mnemonics']}")
        print(f"Total combinations tested: {summary['recovery_stats']['total_combinations']}")
        print(f"Unique addresses generated: {summary['address_stats']['total_unique']}")
        print(f"Seed methods used: {', '.join(summary['recovery_stats']['seed_methods_used'])}")
    
    def process_seeds_file(self, input_file: str, output_file: str):
        """Process seeds from file"""
        print(f"\n{Fore.YELLOW}Processing NON-BIP39 seeds from {input_file}{Style.RESET_ALL}")
        
        # Create output directory
        output_dir = self.create_output_directory()
        
        # Read seeds
        try:
            with open(input_file, 'r', encoding='utf-8') as f:
                content = f.read().strip()
                
                # Handle different formats
                if ',' in content:
                    # CSV format
                    f.seek(0)
                    reader = csv.DictReader(f)
                    mnemonics = [row.get('mnemonic', row.get('seed', '')) for row in reader]
                else:
                    # Text format
                    mnemonics = [line.strip() for line in content.split('\n') if line.strip() and not line.startswith('#')]
                    
        except FileNotFoundError:
            print(f"{Fore.RED}Input file {input_file} not found{Style.RESET_ALL}")
            return
        except Exception as e:
            print(f"{Fore.RED}Error reading input file: {e}{Style.RESET_ALL}")
            return
        
        all_results = []
        
        # Process each mnemonic
        for i, mnemonic in enumerate(mnemonics, 1):
            if mnemonic:
                print(f"\n{Fore.CYAN}{'='*80}")
                print(f"Processing seed {i}/{len(mnemonics)}")
                print(f"{'='*80}{Style.RESET_ALL}")
                
                results = self.process_mnemonic_with_all_methods(mnemonic)
                all_results.extend(results)
        
        # Export results
        if all_results:
            output_base = os.path.join(output_dir, output_file.rsplit('.', 1)[0])
            
            # Export main results
            self._export_main_results(all_results, output_base)
            
            # Export addresses only
            self.export_addresses_only(output_base)
            
            # Check balances
            check = input("\nCheck balances for all addresses? (y/n): ").strip().lower()
            if check == 'y':
                self.check_all_balances(all_results, output_base)
            
            # Generate summary
            self.generate_summary(all_results, output_base)
            
        else:
            print(f"{Fore.RED}No results generated{Style.RESET_ALL}")
    
    def _export_main_results(self, results: List[Dict], output_base: str):
        """Export main recovery results"""
        fieldnames = [
            'mnemonic', 'seed_method', 'method', 'path_name', 'path_str', 'compressed',
            'private_key_hex', 'wif', 'wif_uncompressed', 'public_key_hex',
            'p2pkh_address', 'p2pkh_uncompressed', 'p2sh_address', 'p2wpkh_address',
            'p2wsh_address', 'p2tr_address', 'tron_address'
        ]
        
        csv_file = f"{output_base}_recovery_results.csv"
        with open(csv_file, 'w', newline='', encoding='utf-8') as f:
            writer = csv.DictWriter(f, fieldnames=fieldnames, extrasaction='ignore')
            writer.writeheader()
            writer.writerows(results)
        
        print(f"\n{Fore.GREEN}✓ Recovery results saved to {csv_file}{Style.RESET_ALL}")
        print(f"Total combinations: {len(results)}")

def main():
    """Main function"""
    print(f"{Fore.CYAN}{'='*80}")
    print(f"Universal NON-BIP39 Cryptocurrency Wallet Recovery System v4.0")
    print(f"Complete Bitcoin & TRON Address Generation with USDT Support")
    print(f"{'='*80}{Style.RESET_ALL}")
    
    print(f"\n{Fore.YELLOW}This tool generates ALL possible address types:{Style.RESET_ALL}")
    print("- Bitcoin: P2PKH, P2SH, P2WPKH, P2WSH, P2TR (compressed & uncompressed)")
    print("- TRON: Native addresses with TRC20 token support (including USDT)")
    print("- Multiple seed derivation methods")
    print("- Comprehensive BIP32 paths")
    
    # Check for input file
    if not os.path.exists('seeds.csv'):
        print(f"\n{Fore.YELLOW}Creating sample seeds.csv file...{Style.RESET_ALL}")
        with open('seeds.csv', 'w', encoding='utf-8') as f:
            f.write("word1 word2 word3 word4 word5 word6 word7 word8 word9 word10 word11 word12\n")
            f.write("# Add your NON-BIP39 seed phrases here, one per line\n")
            f.write("# Words should be space-separated\n")
        print(f"{Fore.GREEN}✓ Created seeds.csv - please add your seed phrases{Style.RESET_ALL}")
        return
    
    # Process seeds
    recovery = EnhancedNonBIP39Recovery()
    recovery.process_seeds_file('seeds.csv', 'recovery_results.csv')
    
    print(f"\n{Fore.GREEN}✓ Recovery complete!{Style.RESET_ALL}")

if __name__ == "__main__":
    main()
