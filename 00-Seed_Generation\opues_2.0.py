#!/usr/bin/env python3
"""
BIP39 Mnemonic Seed Phrase Generator
------------------------------------
Generates cryptographically secure BIP39-compatible mnemonic seed phrases.
Creates 12-word seed phrases using the standard BIP39 English wordlist.
Each phrase is generated with 128 bits of entropy and includes proper checksum validation.

Features:
- Generate BIP39-compliant 12-word seed phrases
- Cryptographically secure random number generation
- Checksum calculation and verification
- Option to save generated phrases to file
- Mnemonic validation functionality
- Configurable number of phrases (default: 100)
- Unix timestamp added to default output filename only

Writer: <PERSON><PERSON>
Co-writer: AI Assistant
Date: 2023-11-15
Version: 2.0
"""

import os
import hashlib
import binascii
import argparse
import time
from pathlib import Path

def load_wordlist(file_path):
    """Load BIP39 wordlist from file"""
    with open(file_path, 'r', encoding='utf-8') as file:
        words = [line.strip() for line in file]
    if len(words) != 2048:
        raise ValueError("Invalid wordlist: must contain exactly 2048 words")
    return words

def generate_entropy(bits=128):
    """Generate cryptographically secure random entropy"""
    if bits % 8 != 0:
        raise ValueError("Entropy must be multiple of 8 bits")
    return os.urandom(bits // 8)

def calculate_checksum(entropy):
    """Calculate checksum for entropy"""
    entropy_bits = len(entropy) * 8
    hash_bytes = hashlib.sha256(entropy).digest()
    hash_bits = bin(int.from_bytes(hash_bytes, byteorder='big'))[2:].zfill(256)
    return hash_bits[:entropy_bits // 32]

def entropy_to_mnemonic(entropy, wordlist):
    """Convert entropy to BIP39 mnemonic phrase"""
    entropy_bits = bin(int.from_bytes(entropy, byteorder='big'))[2:].zfill(len(entropy)*8)
    checksum = calculate_checksum(entropy)
    combined = entropy_bits + checksum

    # Split into 11-bit segments
    segments = [combined[i:i+11] for i in range(0, len(combined), 11)]

    # Convert segments to words
    mnemonic = []
    for segment in segments:
        index = int(segment, 2)
        mnemonic.append(wordlist[index])

    return ' '.join(mnemonic)

def validate_mnemonic(mnemonic, wordlist):
    """Validate a BIP39 mnemonic phrase"""
    words = mnemonic.split()
    if len(words) not in [12, 15, 18, 21, 24]:
        return False

    # Convert words to indices
    indices = []
    for word in words:
        try:
            indices.append(wordlist.index(word))
        except ValueError:
            return False

    # Convert indices to bits
    bits = ''.join([bin(index)[2:].zfill(11) for index in indices])

    # Split into entropy and checksum
    entropy_bits = len(words) * 11 // 33 * 32
    entropy_bytes = int(bits[:entropy_bits], 2).to_bytes(entropy_bits // 8, 'big')
    checksum = bits[entropy_bits:]

    # Verify checksum
    calculated_checksum = calculate_checksum(entropy_bytes)
    return checksum == calculated_checksum

def generate_seed_phrases(wordlist_file, count=100, output_file=None, use_timestamp=False):
    """Generate multiple BIP39 seed phrases"""
    wordlist = load_wordlist(wordlist_file)
    phrases = []

    for _ in range(count):
        entropy = generate_entropy(128)
        mnemonic = entropy_to_mnemonic(entropy, wordlist)
        phrases.append(mnemonic)

    # Save to file if requested
    if output_file:
        # Add Unix timestamp only if using default filename
        if use_timestamp:
            timestamp = int(time.time())
            path = Path(output_file)
            stem = path.stem
            suffix = path.suffix
            final_filename = f"{stem}_{timestamp}{suffix}"
        else:
            final_filename = output_file
            
        with open(final_filename, 'w', encoding='utf-8') as file:
            for phrase in phrases:
                file.write(phrase + '\n')
        return phrases, final_filename
    
    return phrases, None

def main():
    # Set up command-line argument parsing
    parser = argparse.ArgumentParser(
        description="Generate BIP39 mnemonic seed phrases",
        formatter_class=argparse.ArgumentDefaultsHelpFormatter
    )
    parser.add_argument(
        '-n', '--count',
        type=int,
        default=100,
        help="Number of seed phrases to generate"
    )
    parser.add_argument(
        '-o', '--output',
        nargs='?',
        const=None,
        default='default',
        help="Output file name for generated phrases (if not specified, uses seed_phrases_[timestamp].txt)"
    )
    parser.add_argument(
        '-w', '--wordlist',
        default="bip39-english.csv",
        help="Path to BIP39 wordlist file"
    )
    args = parser.parse_args()

    # Determine output filename and whether to use timestamp
    if args.output == 'default' or args.output is None:
        # User didn't specify -o flag or used it without value, use default with timestamp
        output_file = "seed_phrases.txt"
        use_timestamp = True
    else:
        # User specified a custom filename, use it exactly
        output_file = args.output
        use_timestamp = False

    # Generate seed phrases
    print(f"Generating {args.count} BIP39 seed phrases...")
    phrases, output_filename = generate_seed_phrases(
        wordlist_file=args.wordlist,
        count=args.count,
        output_file=output_file,
        use_timestamp=use_timestamp
    )

    # Display first 5 phrases
    print("\nFirst 5 generated phrases:")
    for i, phrase in enumerate(phrases[:5], 1):
        print(f"{i}. {phrase}")

    # Validate first phrase
    is_valid = validate_mnemonic(phrases[0], load_wordlist(args.wordlist))
    print(f"\nValidation test for first phrase: {'Valid' if is_valid else 'Invalid'}")

    if output_filename:
        print(f"\nAll {args.count} phrases saved to '{output_filename}'")
        if use_timestamp:
            print(f"Unix timestamp: {int(time.time())}")

if __name__ == "__main__":
    main()
