@echo off
setlocal enabledelayedexpansion

:: Initialize loop counter
set loop_count=0

:: Create CSV header if file doesn't exist
if not exist timing_log.csv (
    echo Loop,StartTime,Step1_Time,Step2_Time,Step3_Time,TotalTime > timing_log.csv
)

:main_loop
set /a loop_count+=1

echo.
echo ========================================
echo Starting Loop Iteration: !loop_count!
echo Start Time: %date% %time%
echo ========================================

:: Record start time (simple format)
set start_time=%time%

:: Get date parts for file naming
for /f "tokens=1-3 delims=- " %%a in ("%date%") do (
    set year=%%a
    set month=%%b
    set day=%%c
)

:: Get time parts for file naming  
for /f "tokens=1-3 delims=:." %%a in ("%time%") do (
    set hour=%%a
    set minute=%%b
    set second=%%c
)

set "datetime=%year%%month%%day%_%hour%%minute%%second%"

echo Running opues_2.0.py...
set step1_start=%time%
python d:/Work.AUG/00-Seed_Generation/opues_2.0.py -n 1000 -o seeds_%datetime%.txt
set step1_end=%time%
echo Step 1 (opues_2.0.py) completed at: !step1_end!

echo Running opus_6.1-Non-interactive.py...
set step2_start=%time%
python d:/Work.AUG/01-Seed2Address/batch_process_seeds.py -i seeds_%datetime%.txt -a btc_%datetime%.txt 
set step2_end=%time%
echo Step 2 (opus_6.1-Non-interactive.py) completed at: !step2_end!

echo Running tsv-To-db.03.py...
set step3_start=%time%
python d:/Work.AUG/04-create-btc-dbs/tsv-To-db.04.py 
rem btc_%datetime%.txt
set step3_end=%time%
echo Step 3 (tsv-To-db.04.py) completed at: !step3_end!

:: Record end time
set end_time=%time%

echo.
echo ========================================
echo Loop Iteration !loop_count! Summary:
echo Start: !start_time!
echo Step 1: !step1_start! to !step1_end!
echo Step 2: !step2_start! to !step2_end!
echo Step 3: !step3_start! to !step3_end!
echo End: !end_time!
echo ========================================

:: Log basic timing information to CSV (timestamps only)
echo !loop_count!,!start_time!,!step1_end!,!step2_end!,!step3_end!,!end_time! >> timing_log.csv

:: Check exit code to determine if we should continue
if exist "FOUNDS.CSV" goto :stop
    echo Cleaning up temporary files...
    del *.txt 2>nul
    del *.log 2>nul
    echo Continuing to next iteration...
    echo.
    timeout /t 5
    goto :main_loop

:stop
echo.
echo ========================================
echo Found matching address! Stopping batch.
echo Total iterations completed: !loop_count!
echo Check timing_log.csv for timestamp records
echo ========================================
pause
goto :eof