#!/usr/bin/env bash

# -----------------------------------------------------------------------------
# Script:    do-all2.sh
# Purpose:   Loop through three Python steps, log times, stop only if step 3
#            exits non-zero. Otherwise cleanup and repeat.
# -----------------------------------------------------------------------------

set -euo pipefail

# Initialize loop counter
loop_count=0

# Ensure timing_log.csv exists with header
if [[ ! -f timing_log.csv ]]; then
  echo "Loop,StartTime,Step1_End,Step2_End,Step3_End,EndTime" > timing_log.csv
fi

while true; do
  loop_count=$((loop_count + 1))
  echo
  echo "========================================"
  echo "Starting Loop Iteration: $loop_count"
  start_time_str=$(date '+%Y-%m-%d %H:%M:%S')
  echo "Start Time: $start_time_str"
  echo "========================================"

  # For total-time calculation
  start_epoch=$(date +%s)
  datetime=$(date '+%Y%m%d_%H%M%S')

  # --- Step 1 ---
  echo "Running opues_2.0.py..."
  step1_start=$(date '+%Y-%m-%d %H:%M:%S')
  python3 src/opues_2.0.py -n 100000 -o seeds_"$datetime".txt
  step1_end=$(date '+%Y-%m-%d %H:%M:%S')
  echo "Step 1 completed at: $step1_end"

  # --- Step 2 ---
  echo "Running opus_6.1-Non-interactive.py..."
  step2_start=$(date '+%Y-%m-%d %H:%M:%S')
  python3 src/opus_6.1-Non-interactive.py \
    seeds_"$datetime".txt \
    btc_"$datetime".txt \
    tron_"$datetime".txt
  step2_end=$(date '+%Y-%m-%d %H:%M:%S')
  echo "Step 2 completed at: $step2_end"

  # --- Step 3 ---
  echo "Running tsv-To-db.03.py..."
  step3_start=$(date '+%Y-%m-%d %H:%M:%S')
  python3 src/tsv-To-db.04.py btc_"$datetime".txt
  rc3=$?
  step3_end=$(date '+%Y-%m-%d %H:%M:%S')
  echo "Step 3 completed at: $step3_end (exit code $rc3)"

  # Record end time and total duration
  end_time_str=$(date '+%Y-%m-%d %H:%M:%S')
  end_epoch=$(date +%s)
  total_sec=$((end_epoch - start_epoch))

  # Print iteration summary
  echo
  echo "========================================"
  echo "Loop Iteration $loop_count Summary:"
  echo "  Start   : $start_time_str"
  echo "  Step 1  : $step1_start → $step1_end"
  echo "  Step 2  : $step2_start → $step2_end"
  echo "  Step 3  : $step3_start → $step3_end"
  echo "  End     : $end_time_str"
  echo "  Duration: ${total_sec}s"
  echo "========================================"

  # Append to CSV
  echo "$loop_count,$step1_end,$step2_end,$step3_end,$end_time_str" \
    >> timing_log.csv

  # If step 3 indicated “found matching address” (exit code ≠ 0), stop here
  if [ -f "FOUNDS.CSV" ]; then
    echo
    echo "========================================"
    echo "Found matching address! Stopping loop."
    echo "Total iterations completed: $loop_count"
    echo "Check timing_log.csv for timestamp records."
    echo "========================================"
    read -r -p "Press [Enter] to exit..."
    exit 0
  fi

  # Otherwise, cleanup and repeat
  echo "Cleaning up temporary files..."
  rm -f *.txt *.log
  echo "Next iteration starts in 5 seconds..."
  sleep 5
done
