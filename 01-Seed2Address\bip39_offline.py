#!/usr/bin/env python3
"""
Offline BIP39 Mnemonic to Bitcoin Address Generator
Generates Bitcoin addresses from BIP39 mnemonic phrases for various derivation paths.
"""

import hashlib
import hmac
import binascii
import struct
import csv
from typing import List, Tuple, Dict
import base58

# ECDSA and Bitcoin utilities
import ecdsa
from ecdsa.curves import SECP256k1
from ecdsa.ellipticcurve import Point

class BIP39:
    def __init__(self, wordlist_file: str = "bip39-english.csv"):
        """Initialize BIP39 with English wordlist"""
        self.wordlist = self._load_wordlist(wordlist_file)
        
    def _load_wordlist(self, filename: str) -> List[str]:
        """Load BIP39 wordlist from CSV file"""
        wordlist = []
        try:
            with open(filename, 'r', encoding='utf-8') as f:
                reader = csv.reader(f)
                for row in reader:
                    if row:  # Skip empty rows
                        wordlist.append(row[0].strip())
        except FileNotFoundError:
            raise FileNotFoundError(f"Wordlist file {filename} not found")
        
        if len(wordlist) != 2048:
            raise ValueError(f"Invalid wordlist length: {len(wordlist)}, expected 2048")
        
        return wordlist
    
    def mnemonic_to_seed(self, mnemonic: str, passphrase: str = "") -> bytes:
        """Convert mnemonic to seed using PBKDF2"""
        mnemonic_bytes = mnemonic.encode('utf-8')
        salt = ('mnemonic' + passphrase).encode('utf-8')
        
        # PBKDF2 with 2048 iterations
        seed = hashlib.pbkdf2_hmac('sha512', mnemonic_bytes, salt, 2048)
        return seed
    
    def validate_mnemonic(self, mnemonic: str) -> bool:
        """Validate BIP39 mnemonic checksum"""
        words = mnemonic.strip().split()
        if len(words) not in [12, 15, 18, 21, 24]:
            return False
        
        # Convert words to indices
        try:
            indices = [self.wordlist.index(word) for word in words]
        except ValueError:
            return False
        
        # Convert to binary
        binary_str = ''.join(format(idx, '011b') for idx in indices)
        
        # Split entropy and checksum
        entropy_length = len(binary_str) * 32 // 33
        entropy_bits = binary_str[:entropy_length]
        checksum_bits = binary_str[entropy_length:]
        
        # Calculate expected checksum
        entropy_bytes = bytes(int(entropy_bits[i:i+8], 2) for i in range(0, len(entropy_bits), 8))
        hash_bytes = hashlib.sha256(entropy_bytes).digest()
        expected_checksum = format(hash_bytes[0], '08b')[:len(checksum_bits)]
        
        return checksum_bits == expected_checksum

class BIP32:
    """BIP32 Hierarchical Deterministic key derivation"""
    
    HARDENED_OFFSET = 0x80000000
    
    def __init__(self, seed: bytes):
        """Initialize with master seed"""
        self.master_key, self.master_chain_code = self._master_key_from_seed(seed)
    
    def _master_key_from_seed(self, seed: bytes) -> Tuple[bytes, bytes]:
        """Generate master private key and chain code from seed"""
        hmac_result = hmac.new(b"Bitcoin seed", seed, hashlib.sha512).digest()
        return hmac_result[:32], hmac_result[32:]
    
    def _derive_child_key(self, parent_key: bytes, parent_chain_code: bytes, index: int) -> Tuple[bytes, bytes]:
        """Derive child key from parent"""
        if index >= self.HARDENED_OFFSET:
            # Hardened derivation
            data = b'\x00' + parent_key + struct.pack('>I', index)
        else:
            # Non-hardened derivation
            parent_public_key = self._private_to_public(parent_key)
            data = parent_public_key + struct.pack('>I', index)
        
        hmac_result = hmac.new(parent_chain_code, data, hashlib.sha512).digest()
        child_key_int = (int.from_bytes(hmac_result[:32], 'big') + int.from_bytes(parent_key, 'big')) % SECP256k1.order
        child_key = child_key_int.to_bytes(32, 'big')
        child_chain_code = hmac_result[32:]
        
        return child_key, child_chain_code
    
    def _private_to_public(self, private_key: bytes) -> bytes:
        """Convert private key to compressed public key"""
        private_key_int = int.from_bytes(private_key, 'big')
        point = private_key_int * SECP256k1.generator
        
        # Compressed public key format
        if point.y() % 2 == 0:
            return b'\x02' + point.x().to_bytes(32, 'big')
        else:
            return b'\x03' + point.x().to_bytes(32, 'big')
    
    def derive_path(self, path: str) -> Tuple[bytes, bytes, bytes]:
        """Derive key from BIP32 path (e.g., "m/44'/0'/0'/0/0")"""
        if not path.startswith('m/'):
            raise ValueError("Path must start with 'm/'")
        
        current_key = self.master_key
        current_chain_code = self.master_chain_code
        
        path_parts = path[2:].split('/')
        if path_parts == ['']:
            path_parts = []
        
        for part in path_parts:
            if part.endswith("'"):
                index = int(part[:-1]) + self.HARDENED_OFFSET
            else:
                index = int(part)
            
            current_key, current_chain_code = self._derive_child_key(current_key, current_chain_code, index)
        
        public_key = self._private_to_public(current_key)
        return current_key, public_key, current_chain_code

class BitcoinAddress:
    """Bitcoin address generation utilities"""

    @staticmethod
    def hash160(data: bytes) -> bytes:
        """RIPEMD160(SHA256(data))"""
        sha256_hash = hashlib.sha256(data).digest()
        ripemd160 = hashlib.new('ripemd160')
        ripemd160.update(sha256_hash)
        return ripemd160.digest()

    @staticmethod
    def base58check_encode(payload: bytes, version: int = 0) -> str:
        """Base58Check encoding"""
        versioned_payload = bytes([version]) + payload
        checksum = hashlib.sha256(hashlib.sha256(versioned_payload).digest()).digest()[:4]
        return base58.b58encode(versioned_payload + checksum).decode('ascii')

    @staticmethod
    def private_key_to_wif(private_key: bytes, compressed: bool = True) -> str:
        """Convert private key to WIF (Wallet Import Format)"""
        # Bitcoin mainnet version byte
        version_byte = 0x80

        # Add compression flag if compressed
        if compressed:
            extended_key = bytes([version_byte]) + private_key + bytes([0x01])
        else:
            extended_key = bytes([version_byte]) + private_key

        # Add checksum
        checksum = hashlib.sha256(hashlib.sha256(extended_key).digest()).digest()[:4]
        return base58.b58encode(extended_key + checksum).decode('ascii')

    @staticmethod
    def p2pkh_address(public_key: bytes) -> str:
        """Generate P2PKH (Legacy) address"""
        hash160 = BitcoinAddress.hash160(public_key)
        return BitcoinAddress.base58check_encode(hash160, 0x00)

    @staticmethod
    def p2sh_address(script_hash: bytes) -> str:
        """Generate P2SH address"""
        return BitcoinAddress.base58check_encode(script_hash, 0x05)

    @staticmethod
    def p2wpkh_address(public_key: bytes) -> str:
        """Generate P2WPKH (Native SegWit) address"""
        hash160 = BitcoinAddress.hash160(public_key)
        # Bech32 encoding for native SegWit
        return BitcoinAddress.bech32_encode('bc', 0, hash160)

    @staticmethod
    def p2wpkh_p2sh_address(public_key: bytes) -> str:
        """Generate P2WPKH nested in P2SH address"""
        hash160 = BitcoinAddress.hash160(public_key)
        # P2WPKH script: OP_0 <20-byte-pubkey-hash>
        witness_script = bytes([0x00, 0x14]) + hash160
        script_hash = BitcoinAddress.hash160(witness_script)
        return BitcoinAddress.p2sh_address(script_hash)
    
    @staticmethod
    def bech32_encode(hrp: str, witver: int, witprog: bytes) -> str:
        """Bech32 encoding for SegWit addresses"""
        # Simplified bech32 implementation
        CHARSET = "qpzry9x8gf2tvdw0s3jn54khce6mua7l"
        
        def bech32_polymod(values):
            GEN = [0x3b6a57b2, 0x26508e6d, 0x1ea119fa, 0x3d4233dd, 0x2a1462b3]
            chk = 1
            for value in values:
                top = chk >> 25
                chk = (chk & 0x1ffffff) << 5 ^ value
                for i in range(5):
                    chk ^= GEN[i] if ((top >> i) & 1) else 0
            return chk
        
        def bech32_hrp_expand(hrp):
            return [ord(x) >> 5 for x in hrp] + [0] + [ord(x) & 31 for x in hrp]
        
        def bech32_create_checksum(hrp, data):
            values = bech32_hrp_expand(hrp) + data
            polymod = bech32_polymod(values + [0, 0, 0, 0, 0, 0]) ^ 1
            return [(polymod >> 5 * (5 - i)) & 31 for i in range(6)]
        
        def convertbits(data, frombits, tobits, pad=True):
            acc = 0
            bits = 0
            ret = []
            maxv = (1 << tobits) - 1
            max_acc = (1 << (frombits + tobits - 1)) - 1
            for value in data:
                if value < 0 or (value >> frombits):
                    return None
                acc = ((acc << frombits) | value) & max_acc
                bits += frombits
                while bits >= tobits:
                    bits -= tobits
                    ret.append((acc >> bits) & maxv)
            if pad:
                if bits:
                    ret.append((acc << (tobits - bits)) & maxv)
            elif bits >= frombits or ((acc << (tobits - bits)) & maxv):
                return None
            return ret
        
        data = [witver] + convertbits(witprog, 8, 5)
        checksum = bech32_create_checksum(hrp, data)
        return hrp + '1' + ''.join([CHARSET[d] for d in data + checksum])

def generate_addresses(mnemonic: str, passphrase: str = "", num_addresses: int = 10) -> Dict[str, List[Dict]]:
    """Generate Bitcoin addresses for various derivation paths matching the HTML tool exactly"""

    # Initialize BIP39
    bip39 = BIP39()

    # Validate mnemonic
    if not bip39.validate_mnemonic(mnemonic):
        raise ValueError("Invalid mnemonic phrase")

    # Generate seed
    seed = bip39.mnemonic_to_seed(mnemonic, passphrase)

    # Initialize BIP32
    bip32 = BIP32(seed)

    # Define derivation paths exactly as the HTML tool does
    # These are the exact paths that match the user's expected results
    paths = {
        "m/0'/0'/0'": ("BIP32 Custom", "P2PKH"),
        "m/44'/0'/0'/0": ("BIP44 (Legacy)", "P2PKH"),
        "m/49'/0'/0'/0": ("BIP49 (P2WPKH nested in P2SH)", "P2WPKH nested in P2SH"),
        "m/84'/0'/0'/0": ("BIP84 (Native SegWit)", "P2WPKH"),
        "m/0": ("Simple derivation", "P2PKH")
    }

    results = {}

    for base_path, (description, default_script_type) in paths.items():
        addresses = []

        for i in range(num_addresses):
            # Generate paths based on the pattern from your sample data
            if base_path == "m/0'/0'/0'":
                # Pattern: m/0'/0'/0', m/0'/0'/1', m/0'/0'/2', etc. (all hardened)
                full_path = f"m/0'/0'/{i}'"
            elif base_path == "m/44'/0'/0'/0":
                # Pattern: m/44'/0'/0'/0/0', m/44'/0'/0'/0/1', etc. (all hardened)
                full_path = f"m/44'/0'/0'/0/{i}'"
            elif base_path == "m/49'/0'/0'/0":
                # Pattern: m/49'/0'/0'/0/0', m/49'/0'/0'/0/1', etc. (all hardened)
                full_path = f"m/49'/0'/0'/0/{i}'"
            elif base_path == "m/84'/0'/0'/0":
                # Pattern: m/84'/0'/0'/0/0', m/84'/0'/0'/0/1', etc. (all hardened)
                full_path = f"m/84'/0'/0'/0/{i}'"
            elif base_path == "m/0":
                # Pattern: m/0/0', m/0/1', etc. (hardened indices)
                full_path = f"m/0/{i}'"
            else:
                full_path = f"{base_path}/{i}"

            # Derive the key for this specific path
            private_key, public_key, chain_code = bip32.derive_path(full_path)

            # Generate addresses based on path type
            if "44'" in base_path:
                # BIP44 - Legacy P2PKH
                address = BitcoinAddress.p2pkh_address(public_key)
                script_semantics = "P2PKH"

                # Convert private key to WIF format
                private_key_wif = BitcoinAddress.private_key_to_wif(private_key, compressed=True)

                addresses.append({
                    "path": full_path,
                    "address": address,
                    "public_key": public_key.hex(),
                    "private_key": private_key.hex(),
                    "private_key_wif": private_key_wif,
                    "script_semantics": script_semantics
                })

            elif "49'" in base_path:
                # BIP49 - P2WPKH nested in P2SH
                address = BitcoinAddress.p2wpkh_p2sh_address(public_key)
                script_semantics = "P2WPKH nested in P2SH"

                # Convert private key to WIF format
                private_key_wif = BitcoinAddress.private_key_to_wif(private_key, compressed=True)

                addresses.append({
                    "path": full_path,
                    "address": address,
                    "public_key": public_key.hex(),
                    "private_key": private_key.hex(),
                    "private_key_wif": private_key_wif,
                    "script_semantics": script_semantics
                })

            elif "84'" in base_path:
                # BIP84 - Native SegWit
                address = BitcoinAddress.p2wpkh_address(public_key)
                script_semantics = "P2WPKH"

                # Convert private key to WIF format
                private_key_wif = BitcoinAddress.private_key_to_wif(private_key, compressed=True)

                addresses.append({
                    "path": full_path,
                    "address": address,
                    "public_key": public_key.hex(),
                    "private_key": private_key.hex(),
                    "private_key_wif": private_key_wif,
                    "script_semantics": script_semantics
                })

            elif base_path == "m/0":
                # Special case: m/0/X' generates both P2SH and P2WPKH addresses
                # Convert private key to WIF format
                private_key_wif = BitcoinAddress.private_key_to_wif(private_key, compressed=True)

                # P2WPKH nested in P2SH
                p2sh_address = BitcoinAddress.p2wpkh_p2sh_address(public_key)
                addresses.append({
                    "path": full_path,
                    "address": p2sh_address,
                    "public_key": public_key.hex(),
                    "private_key": private_key.hex(),
                    "private_key_wif": private_key_wif,
                    "script_semantics": "P2WPKH nested in P2SH"
                })

                # Native P2WPKH
                p2wpkh_address = BitcoinAddress.p2wpkh_address(public_key)
                addresses.append({
                    "path": full_path,
                    "address": p2wpkh_address,
                    "public_key": public_key.hex(),
                    "private_key": private_key.hex(),
                    "private_key_wif": private_key_wif,
                    "script_semantics": "P2WPKH"
                })

            else:
                # Default to P2PKH for other paths (like m/0'/0'/X')
                address = BitcoinAddress.p2pkh_address(public_key)
                script_semantics = "P2PKH"

                # Convert private key to WIF format
                private_key_wif = BitcoinAddress.private_key_to_wif(private_key, compressed=True)

                addresses.append({
                    "path": full_path,
                    "address": address,
                    "public_key": public_key.hex(),
                    "private_key": private_key.hex(),
                    "private_key_wif": private_key_wif,
                    "script_semantics": script_semantics
                })

        results[f"{base_path} ({description})"] = addresses

    return results

def export_to_csv(results: Dict[str, List[Dict]], filename: str = "bip39_addresses.csv"):
    """Export results to CSV file"""
    with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
        fieldnames = ['derivation_path', 'index', 'address', 'public_key', 'private_key', 'private_key_wif', 'script_semantics']
        writer = csv.DictWriter(csvfile, fieldnames=fieldnames)

        writer.writeheader()
        for path_desc, addresses in results.items():
            base_path = path_desc.split(' (')[0]  # Extract just the path part
            for i, addr_info in enumerate(addresses):
                writer.writerow({
                    'derivation_path': f"{addr_info['path']}",
                    'index': i,
                    'address': addr_info['address'],
                    'public_key': addr_info['public_key'],
                    'private_key': addr_info['private_key'],
                    'private_key_wif': addr_info['private_key_wif'],
                    'script_semantics': addr_info['script_semantics']
                })

def export_addresses_only(results: Dict[str, List[Dict]], filename: str = "bip39_only_addresses.txt"):
    """Export only addresses to text file for balance checking"""
    with open(filename, 'w', encoding='utf-8') as txtfile:
        for path_desc, addresses in results.items():
            for addr_info in addresses:
                txtfile.write(f"{addr_info['address']}\n")

def process_seeds_file(input_file: str = "seeds.txt", csv_output: str = "bip39_addresses.csv",
                      addresses_output: str = "bip39_only_addresses.txt", num_addresses: int = 10):
    """Process multiple seeds from input file and generate addresses"""

    print(f"Processing seeds from: {input_file}")
    print(f"CSV output: {csv_output}")
    print(f"Addresses only output: {addresses_output}")
    print("=" * 60)

    try:
        # Read seeds from input file
        with open(input_file, 'r', encoding='utf-8') as f:
            seeds = [line.strip() for line in f if line.strip()]

        if not seeds:
            print(f"No seeds found in {input_file}")
            return

        print(f"Found {len(seeds)} seed(s) to process")
        print("Processing seeds... (showing progress counter)")
        print()

        # Initialize BIP39 for validation
        bip39 = BIP39()
        processed_count = 0
        error_count = 0

        # Prepare CSV file
        with open(csv_output, 'w', newline='', encoding='utf-8') as csvfile:
            fieldnames = ['seed_index', 'seed', 'derivation_path', 'address_index', 'address',
                         'public_key', 'private_key', 'private_key_wif', 'script_semantics']
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            writer.writeheader()

            # Prepare addresses-only file
            with open(addresses_output, 'w', encoding='utf-8') as txtfile:

                for seed_idx, seed in enumerate(seeds):
                    # Show progress counter instead of detailed output
                    print(f"\rProcessed: {processed_count}/{len(seeds)} | Errors: {error_count}", end='', flush=True)

                    try:
                        # Validate seed (could be mnemonic or hex seed)
                        if len(seed.split()) >= 12:  # Looks like a mnemonic
                            if not bip39.validate_mnemonic(seed):
                                error_count += 1
                                continue
                            results = generate_addresses(seed, num_addresses=num_addresses)
                        else:
                            error_count += 1
                            continue

                        # Write to CSV and addresses file
                        for path_desc, addresses in results.items():
                            for addr_idx, addr_info in enumerate(addresses):
                                # Write to CSV
                                writer.writerow({
                                    'seed_index': seed_idx + 1,
                                    'seed': seed,
                                    'derivation_path': addr_info['path'],
                                    'address_index': addr_idx,
                                    'address': addr_info['address'],
                                    'public_key': addr_info['public_key'],
                                    'private_key': addr_info['private_key'],
                                    'private_key_wif': addr_info['private_key_wif'],
                                    'script_semantics': addr_info['script_semantics']
                                })

                                # Write to addresses-only file
                                txtfile.write(f"{addr_info['address']}\n")

                        processed_count += 1

                    except Exception as e:
                        error_count += 1
                        continue

                # Final progress update
                print(f"\rProcessed: {processed_count}/{len(seeds)} | Errors: {error_count}")
                print()

        print()
        print("=" * 60)
        print("✅ Processing completed!")
        print(f"📁 Full data saved to: {csv_output}")
        print(f"📋 Addresses only saved to: {addresses_output}")

    except FileNotFoundError:
        print(f"❌ Input file '{input_file}' not found!")
        print("Please create the file with one seed (mnemonic) per line.")
    except Exception as e:
        print(f"❌ Error: {e}")

def main():
    """Main function to demonstrate the BIP39 offline tool"""

    # Test data from user
    mnemonic = "motor venture dilemma quote subject magnet keep large dry gossip bean paper"
    expected_seed = "24bd1b243ec776dd97bc7487ad65c8966ff6e0b8654a25602a41994746957c49c813ba183e6d1646584cf810fcb9898f44571e3ccfe9fb266e3a66597fbcd7c4"

    print("BIP39 Offline Address Generator")
    print("=" * 50)
    print(f"Mnemonic: {mnemonic}")
    print()

    # Generate seed and verify
    bip39 = BIP39()
    seed = bip39.mnemonic_to_seed(mnemonic)
    seed_hex = seed.hex()

    print(f"Generated Seed: {seed_hex}")
    print(f"Expected Seed:  {expected_seed}")
    print(f"Seed Match: {seed_hex == expected_seed}")
    print()

    # Generate addresses
    try:
        results = generate_addresses(mnemonic, num_addresses=10)

        # Display results
        for path_desc, addresses in results.items():
            print(f"\n{path_desc}")
            print("-" * len(path_desc))
            print(f"{'Index':<5} | {'Address':<42} | {'Public Key':<20} | {'Private Key':<20} | {'Script Semantics'}")
            print("-" * 130)

            for i, addr_info in enumerate(addresses):
                print(f"{i:<5} | {addr_info['address']:<42} | {addr_info['public_key'][:20]}... | {addr_info['private_key'][:20]}... | {addr_info['script_semantics']}")

        # Export to CSV
        export_to_csv(results)
        print(f"\n✓ Results exported to 'bip39_addresses.csv'")

        # Display summary in the format requested by user
        print(f"\n" + "="*80)
        print("SUMMARY - Derivation Paths and First Address of Each Type:")
        print("="*80)

        summary_paths = [
            ("m/0'/0'/0'", "Legacy (Custom)"),
            ("m/44'/0'/0'/0/0", "BIP44 (Legacy P2PKH)"),
            ("m/49'/0'/0'/0/0", "BIP49 (P2WPKH nested in P2SH)"),
            ("m/84'/0'/0'/0/0", "BIP84 (Native SegWit P2WPKH)"),
            ("m/0/0", "Simple derivation")
        ]

        print(f"{'Path':<20} | {'Address':<42} | {'Script Semantics'}")
        print("-" * 85)

        for path, desc in summary_paths:
            # Find the corresponding result
            for path_desc, addresses in results.items():
                if path.replace('/0', '/0') in addresses[0]['path']:
                    addr_info = addresses[0]
                    print(f"{path:<20} | {addr_info['address']:<42} | {addr_info['script_semantics']}")
                    break

    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    main()
